package com.ifallious.features.spells;
import com.ifallious.Wynnutils;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.ifallious.utils.minecraft.Tick;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.wynntils.StatusEffect;
import com.ifallious.utils.wynntils.StatusEffectUtils;
import meteordevelopment.orbit.EventHandler;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Arrays;

public class AutoRadiance {
    private static long timeSinceLastBash = 0;
    
    public AutoRadiance() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("autoRadiance"))) {
                StatusEffect radiance = StatusEffectUtils.getStatusEffect("Radiant");
                if (radiance == null) return;
                if (radiance.isIncluded() && radiance.getSeconds() == 1 && StatusEffectUtils.getStatusEffect("Radiance") == null && Tick.since(timeSinceLastBash) > 60) {
                    int OriginalSlot = Wynnutils.mc.player.getInventory().selectedSlot;
                    timeSinceLastBash = Tick.now();
                    int VeritasSlot = Utils.getSlot("Veritas");
                    Tick.schedule(15, () -> {
                        try {
                            Wynnutils.mc.player.getInventory().setSelectedSlot(VeritasSlot != -1 ? VeritasSlot : 0);
                        } catch (Exception e) {
                            ErrorReporter.reportError("AutoRadiance slot selection failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        }
                    });
                    Tick.schedule(16, () -> {
                        try {
                            SpellmacroUtils.add(Arrays.asList("R","L","R"));
                        } catch (Exception e) {
                            ErrorReporter.reportError("AutoRadiance spell addition failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        }
                    });
                    Tick.schedule(30, () -> {
                        try {
                            Wynnutils.mc.player.getInventory().setSelectedSlot(OriginalSlot);
                        } catch (Exception e) {
                            ErrorReporter.reportError("AutoRadiance slot restoration failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        }
                    });
                }
            }
        } catch (Exception e) {
            ErrorReporter.reportError("AutoRadiance tick event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}
