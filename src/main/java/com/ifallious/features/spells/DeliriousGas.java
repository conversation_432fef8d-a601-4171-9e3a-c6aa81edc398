package com.ifallious.features.spells;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.ifallious.utils.minecraft.Tick;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.wynntils.StatusEffect;
import com.ifallious.utils.wynntils.StatusEffectUtils;
import meteordevelopment.orbit.EventHandler;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Arrays;

public class DeliriousGas {
    public static boolean isActive = false;
    public DeliriousGas() {
        GlobalEventBus.subscribe(this);
    }
    @EventHandler
    public void onTick(TickEvent event) {
        try {
            if (Boolean.TRUE.equals(ConfigManager.getFeature("deliriousGas"))) {
                StatusEffect DeliriousGas = StatusEffectUtils.getStatusEffect("Delirious Gas");
                if (DeliriousGas == null) return;
                if (DeliriousGas.isIncluded() && !isActive) {
                    isActive = true;
                    Tick.schedule(95, () -> {
                        try {
                            SpellmacroUtils.add(Arrays.asList("R","R","L"));
                        } catch (Exception e) {
                            ErrorReporter.reportError("DeliriousGas spell addition failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                        }
                        isActive = false;
                    });
                }
            }
        } catch (Exception e) {
            ErrorReporter.reportError("DeliriousGas tick event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
}
