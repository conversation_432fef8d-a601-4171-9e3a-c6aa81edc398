package com.ifallious.features.raids;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.ParticleEvent;
import com.ifallious.event.RaidEvent;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigManager;
import com.wynntils.core.components.Models;
import gg.essential.universal.UChat;
import gg.essential.universal.wrappers.UPlayer;
import gg.essential.universal.wrappers.message.UTextComponent;
import meteordevelopment.orbit.EventHandler;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.text.ClickEvent;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

public class TCCExitLocator {
    private final List<ExitLocation> exitLocations = new ArrayList<>();
    private long lastMessage = 0;
    private Boolean messageSent = false;
    private String partyMessage = "";

    public TCCExitLocator() {
        KeyBindingHelper.registerKeyBinding(new KeyBinding("Exit Relay", InputUtil.Type.KEYSYM, GLFW.GLFW_KEY_UNKNOWN, "Wynnutils"));
        GlobalEventBus.subscribe(this);
        exitLocations.add(new ExitLocation(10839,3512, "Red"));
        exitLocations.add(new ExitLocation(10803,3449, "Red"));
        exitLocations.add(new ExitLocation(10826,3396, "Red"));
        exitLocations.add(new ExitLocation(10858,3370, "Red"));
        exitLocations.add(new ExitLocation(10824,3348, "Red"));
        exitLocations.add(new ExitLocation(10900,3417, "Green"));
        exitLocations.add(new ExitLocation(10879,3432, "Green"));
        exitLocations.add(new ExitLocation(10859,3389, "Green"));
        exitLocations.add(new ExitLocation(10957,3388, "Green"));
        exitLocations.add(new ExitLocation(11026,3550, "Yellow"));
        exitLocations.add(new ExitLocation(11029,3580, "Yellow"));
        exitLocations.add(new ExitLocation(11050,3550, "Yellow"));
        exitLocations.add(new ExitLocation(10974,3526, "Yellow"));
        exitLocations.add(new ExitLocation(10917,3533, "Yellow"));
        exitLocations.add(new ExitLocation(11101,3456, "Blue"));
        exitLocations.add(new ExitLocation(11117,3475, "Blue"));
        exitLocations.add(new ExitLocation(11125,3530, "Blue"));
        exitLocations.add(new ExitLocation(11105,3532, "Blue"));
        exitLocations.add(new ExitLocation(11065,3497, "Blue"));
        exitLocations.add(new ExitLocation(11031,3441, "Blue"));
    }

    @EventHandler
    public void onParticle(ParticleEvent event) {
        if (!event.parameters.getType().equals(ParticleTypes.WITCH) || !ConfigManager.getFeature("tccExitLocator")) return;
        if (Models.Raid.getCurrentRoomName().equals("Labyrinth")) {
            ClosestLocationResult closestLocationResult = closestLocation(event.x, event.z);
            if (closestLocationResult != null && closestLocationResult.distance < 15 && System.currentTimeMillis() - lastMessage > 60000) {
                Utils.simulateChat("§5§l[Wynnutils] §rExit found! Color: " + closestLocationResult.color + " At: [" + (int) event.x + " " + (int) event.y + " " + (int) event.z + "]");
                UTextComponent message = new UTextComponent("§e§nClick here to send Message to Party!");
                message.setClick(ClickEvent.Action.RUN_COMMAND, "/p " + closestLocationResult.color + " " + (int) event.x + " " + (int) event.y + " " + (int) event.z);
                message.chat();
                UPlayer.getPlayer().playSoundToPlayer(SoundEvents.BLOCK_AMETHYST_BLOCK_BREAK, SoundCategory.MASTER, 1.0f, 1.0f);
                partyMessage = "p " + closestLocationResult.color + " " + (int) event.x + " " + (int) event.y + " " + (int) event.z;
                lastMessage = System.currentTimeMillis();
            }
        }
    }

    private static class ClosestLocationResult {
        public final double x;
        public final double z;
        public final String color;
        public final double distance;
        public ClosestLocationResult(double x, double z, String color, double distance) {
            this.x = x;
            this.z = z;
            this.color = color;
            this.distance = distance;
        }
    }

    private ClosestLocationResult closestLocation(double x, double z) {
        if (exitLocations.isEmpty()) {
            return null;
        }
        ExitLocation closestLocation = null;
        double closestDistance = Double.MAX_VALUE;

        for (ExitLocation exitLocation : exitLocations) {
            double targetX = exitLocation.x;
            double targetZ = exitLocation.z;
            double distance = Math.sqrt(Math.pow(targetX - x, 2) + Math.pow(targetZ - z, 2));

            if (distance < closestDistance) {
                closestDistance = distance;
                closestLocation = exitLocation;
            }
        }
        return new ClosestLocationResult(closestLocation.x, closestLocation.z, closestLocation.color, closestDistance);
    }
    private static class ExitLocation {
        final double x;
        final double z;
        final String color;

        ExitLocation(double x, double z, String color) {
            this.x = x;
            this.z = z;
            this.color = color;
        }
    }

}
