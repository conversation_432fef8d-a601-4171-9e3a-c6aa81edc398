package com.ifallious.gui.subscreens;

import com.ifallious.gui.components.ConfigOption;
import gg.essential.elementa.UIComponent;

public class MiscCategoryScreen extends CategoryConfigScreen {
    public MiscCategoryScreen(UIComponent parent, <PERSON><PERSON><PERSON> first) {
        super(parent, "Misc. Category", first);
    }

    @Override
    protected void initOptions() {
        addOption(new ConfigOption(
                "Anti Blind",
                "antiBlind",
                "Prevents you from being affected by Blindness",
                true
        ));
        addOption(new ConfigOption(
                "Trap Timer",
                "trapTimer",
                "Displays a timer above Traps",
                false
        ));
        addOption(new ConfigOption(
                "Stronger Charge",
                "strongerCharge",
                "Enhances the knockback effect of the Charge spell.",
                true
        ));
        addOption(new ConfigOption(
                "Charge Velocity Multiplier",
                "chargeVelocityMultiplier",
                "Multiplier for the velocity of the Charge spell. (Default: 1.2)",
                1.2,
                0.5,
                2.0
        ));
    }
}
