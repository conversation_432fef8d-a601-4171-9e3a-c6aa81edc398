package com.ifallious.gui.subscreens;

import com.ifallious.gui.components.ConfigOption;
import gg.essential.elementa.UIComponent;

public class WarCategoryScreen extends CategoryConfigScreen {
    public WarCategoryScreen(UIComponent parent, <PERSON><PERSON><PERSON> first) {
        super(parent, "War Category", first);
    }

    @Override
    protected void initOptions() {
        addOption(new ConfigOption(
                "Jump Height",
                "jumpHeight",
                "Enables the jump height feature, allowing you to jump higher in war.",
                true
        ));

        addOption(new ConfigOption(
                "Aura Dodge",
                "auraDodge",
                "Automatically jumps when an aura is about to spawn.",
                true
        ));

        addOption(new ConfigOption(
                "Delirious Gas",
                "deliriousGas",
                "Automatically casts Delirious Gas when you are low on health.",
                false
        ));

        addOption(new ConfigOption(
                "Auto Radiance",
                "autoRadiance",
                "Automatically casts Radiance when you are low on health.",
                false
        ));
    }
}
