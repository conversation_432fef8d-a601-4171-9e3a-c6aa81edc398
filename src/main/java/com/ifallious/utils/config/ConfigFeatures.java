package com.ifallious.utils.config;

/**
 * Configuration class for all toggleable features in the mod
 *
 * All fields are public for direct access, eliminating the need for getters and setters.
 * The ConfigManager uses reflection to access these fields by name.
 */
public class ConfigFeatures {
    public boolean tccSwordNotification = true;
    public boolean rareMobNotification = true;
    public boolean keyGuardianNotification;
    public boolean desktopNotifications;
    public boolean spellMacro = true;
    public boolean spellMacroHold = false;
    public boolean corruptedCooldown = true;
    public boolean autoRadiance = false;
    public boolean auraDodge = true;
    public boolean jumpHeight = false;
    public boolean deliriousGas = false;
    public boolean recastTotem = false;
    public boolean antiBlind = true;
    public boolean strongerCharge = true;
    public boolean tccExitLocator = true;
    public boolean trapTimer = false;
    public boolean autoTransfer = false;
    public boolean guildResponse = false;
    public boolean AITools = true;

    public ConfigFeatures() {
    }
}
