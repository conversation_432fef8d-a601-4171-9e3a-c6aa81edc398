package com.ifallious.utils.ai.tools.impl;

import com.ifallious.utils.ai.tools.*;
import gg.essential.universal.UChat;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.Map;

public class <PERSON><PERSON>craftPlayerAPI implements Tool {
        @Override
        public String getName() {
            return "get_wynncraft_player";
        }

        @Override
        public String getDescription() {
            return """
                    Gets all information of a Player on Wynncraft from the API use this with the username of the player requesting unless specified in the Users Message
                    Response:
                    {
                        "username": str,
                        "online": bool,
                        "server": str,
                        "activeCharacter": str or None,
                        "uuid": str,
                        "rank": str,
                        "rankBadge": str,  # URL to the badge SVG in the Wynncraft CDN (only path)
                        "legacyRankColour": {
                            "main": str,
                            "sub": str
                        },
                        "shortenedRank": str,
                        "supportRank": str,
                        "veteran": bool,
                        "firstJoin": str,
                        "lastJoin": str,
                        "playtime": float,
                        "guild": {
                            "name": str,
                            "prefix": str,
                            "rank": str,
                            "rankStars": str
                        },
                        "globalData": {
                            "wars": int,
                            "totalLevels": int,
                            "killedMobs": int,
                            "chestsFound": int,
                            "dungeons": {
                                "total": int,
                                "list": {
                                    "Dungeon Name": int  # Number of total completions on all characters
                                    [...]
                                }
                            },
                            "raids": {
                                "total": int,
                                "list": {
                                    "Raid Name": int  # Number of total completions on all characters
                                    [...]
                                }
                            },
                            "completedQuests": int,
                            "pvp": {
                                "kills": int,
                                "deaths": int
                            }
                        },
                        "forumLink": int or None,
                        "ranking": {
                            "Ranking Type": int
                            [...]
                        },
                        "previousRanking": {
                            "Ranking Type": int
                            [...]
                        },
                        "publicProfile": bool,
                        "characters": {
                            "characterUuid": {
                                "type": str,
                                "nickname": str,
                                "level": int,
                                "xp": int,
                                "xpPercent": int,
                                "totalLevel": int,
                                "wars": int,
                                "playtime": float,
                                "mobsKilled": int,
                                "chestsFound": int,
                                "blocksWalked": int,
                                "itemsIdentified": int,
                                "logins": int,
                                "deaths": int,
                                "discoveries": int,
                                "pvp": {
                                    "kills": int,
                                    "deaths": int,
                                },
                                "gamemode": [
                                    "hunted",
                                    "hardcore",
                                    [...]
                                ],
                                "skillPoints": {
                                    "strength": int,
                                    "dexterity": int,
                                    "intelligence": int,
                                    "defence": int,
                                    "agility": int
                                },
                                "professions": {
                                    "fishing": {
                                        "level": int,
                                        "xpPercent": int
                                    },
                                    "mining": {
                                        "level": int,
                                        "xpPercent": int
                                    },
                                    # [...]
                                },
                                "dungeons": {
                                    "total": int,
                                    "list": {
                                        "Dungeon Name": int
                                        # [...]
                                    }
                                },
                                "raids": {
                                    "total": int,
                                    "list": {
                                        "Raid Name": int
                                        # [...]
                                    }
                                },
                                "quests": [
                                    "Quest Name",
                                    [...]
                                ],
                            },
                            # [...]
                        },
                    }
                    """;

        }

        @ToolExecute
        public String execute(
                @ToolParameter(
                        name = "username",
                        type = "string",
                        description = "The Username to query the API with",
                        required = true
                ) String username
        ) throws IOException {
            String url = "https://api.wynncraft.com/v3/player/"+ username + "?fullResult";
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            Response response = client.newCall(request).execute();
            return response.body().string();
        }
    }
