package com.ifallious.utils.ai.tools;

import com.ifallious.utils.ai.ToolCallingConversationManager;
import com.ifallious.utils.ai.ConversationManager;
import gg.essential.universal.UChat;

/**
 * Test command to demonstrate tool calling functionality.
 * This shows how to use the framework in practice.
 */
public class ToolTestCommand {
    
    /**
     * Test the tool calling framework with various examples
     */
    public static void runTests() {
        UChat.chat("Starting AI Tool Tests...");
        
        // Create conversation manager with tools enabled
        ToolCallingConversationManager manager = ToolCallingConversationManager.create(500);
        if (manager == null) {
            UChat.chat("Failed to create conversation manager - check AI settings");
            return;
        }
        
        manager.withToolsEnabled(true)
               .withSystemPrompt("You are a helpful assistant with access to tools. " +
                               "Use the available tools when appropriate to answer questions.");

        // Test 1: Calculator
        testCalculator(manager);
        
        // Test 2: Wikipedia (if you want to test it)
        // testWikipedia(manager);
        
        // Test 3: Minecraft Info
        testMinecraftInfo(manager);
    }
    
    private static void testCalculator(ToolCallingConversationManager manager) {
        UChat.chat("Testing Calculator Tool...");

        manager.sendMessage("What is 15 + 27? Please use the calculator tool to compute this.")
               .thenAccept(response -> {
                   UChat.chat("Calculator Test Result: " + response);
               })
               .exceptionally(throwable -> {
                   UChat.chat("Calculator Test Failed: " + throwable.getMessage());
                   throwable.printStackTrace();
                   return null;
               });
    }
    
    private static void testWikipedia(ToolCallingConversationManager manager) {
        UChat.chat("Testing Wikipedia Tool...");
        
        manager.sendMessage("Tell me about Minecraft")
               .thenAccept(response -> {
                   UChat.chat("Wikipedia Test Result: " + response);
               })
               .exceptionally(throwable -> {
                   UChat.chat("Wikipedia Test Failed: " + throwable.getMessage());
                   return null;
               });
    }
    
    private static void testMinecraftInfo(ToolCallingConversationManager manager) {
        UChat.chat("Testing Minecraft Info Tool...");
        
        manager.sendMessage("What's my current position in the game?")
               .thenAccept(response -> {
                   UChat.chat("Minecraft Info Test Result: " + response);
               })
               .exceptionally(throwable -> {
                   UChat.chat("Minecraft Info Test Failed: " + throwable.getMessage());
                   return null;
               });
    }
    
    /**
     * Test individual tools directly (without AI conversation)
     */
    public static void testToolsDirect() {
        UChat.chat("Testing tools directly...");

        // Test calculator
        ToolRegistry.executeTool("calculate", "{\"operation\":\"add\",\"a\":10,\"b\":5}")
                   .thenAccept(result -> UChat.chat("Direct Calculator Test: " + result));

        // Test minecraft info
        ToolRegistry.executeTool("get_minecraft_info", "{\"info_type\":\"position\"}")
                   .thenAccept(result -> UChat.chat("Direct Minecraft Test: " + result));
    }

    /**
     * Test with regular ConversationManager (with tools enabled)
     */
    public static void testWithRegularManager() {
        UChat.chat("Testing with regular ConversationManager...");

        ConversationManager manager = ConversationManager.create(500);
        if (manager == null) {
            UChat.chat("Failed to create conversation manager - check AI settings");
            return;
        }

        manager.withToolsEnabled(true)
               .withSystemPrompt("You are a helpful assistant with access to tools. " +
                               "Use the available tools when appropriate to answer questions.");

        manager.sendMessage("What is 15 + 27? Please use the calculator tool to compute this.")
               .thenAccept(response -> {
                   UChat.chat("Regular Manager Test Result: " + response);
               })
               .exceptionally(throwable -> {
                   UChat.chat("Regular Manager Test Failed: " + throwable.getMessage());
                   throwable.printStackTrace();
                   return null;
               });
    }
    
    /**
     * List all available tools
     */
    public static void listTools() {
        UChat.chat("Available AI Tools:");
        for (String toolName : ToolRegistry.getRegisteredToolNames()) {
            UChat.chat(" - " + toolName);
        }
        UChat.chat("Total: " + ToolRegistry.getRegisteredToolNames().size() + " tools");
    }
}
